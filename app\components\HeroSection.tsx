'use client';

import { useEffect, useRef, useState } from 'react';

const HeroSection = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [videoError, setVideoError] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      const handleLoadedData = () => {
        setVideoLoaded(true);
        video.play().catch((error) => {
          console.log('Video autoplay failed:', error);
        });
      };

      const handleError = () => {
        console.error('Video failed to load');
        setVideoError(true);
      };

      video.addEventListener('loadeddata', handleLoadedData);
      video.addEventListener('error', handleError);

      // Try to load the video
      video.load();

      return () => {
        video.removeEventListener('loadeddata', handleLoadedData);
        video.removeEventListener('error', handleError);
      };
    }
  }, []);

  return (
    <section id="home" className="relative h-screen w-full overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0 w-full h-full">
        {!videoError ? (
          <video
            ref={videoRef}
            className={`w-full h-full object-cover transition-opacity duration-1000 ${
              videoLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            autoPlay
            muted
            loop
            playsInline
            preload="auto"
            poster="/img/Foto/Shooting/output/_DSC0189.webp"
          >
            <source src="/video/video_slider.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : null}

        {/* Fallback background image */}
        {(!videoLoaded || videoError) && (
          <div
            className="absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: "url('/img/Foto/Shooting/output/_DSC0189.webp')"
            }}
          />
        )}

        {/* Enhanced multi-layer overlay for optimal text readability */}
        <div className="absolute inset-0 bg-black bg-opacity-25"></div>

        {/* Primary blur overlay for text contrast */}
        <div className="absolute inset-0  bg-black bg-opacity-5"></div>

        {/* Secondary focused blur for center content area */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/30 to-black/20"></div>

        {/* Subtle vignette effect */}
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-transparent to-black/40"></div>
      </div>

      {/* Content overlay - minimal as requested */}
      <div className="relative z-10 h-full flex items-center justify-center">
        <div className="text-center backdrop-blur-md px-4 py-8 md:hidden">
          {/* Optional: Add minimal branding or call-to-action here if needed */}
          <div className="animate-fade-in-up">
            <h1 className="font-syne text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight">
              36K AGENCY
            </h1>
            <p className="font-inter text-lg md:text-xl text-text-secondary max-w-2xl mx-auto mb-8">
              Agenzia creativa specializzata in social media management, content creation e strategie di marketing
            </p>
            <button
              onClick={() => {
                const element = document.querySelector('#about');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="bg-gradient-primary text-white px-8 py-4 rounded-full font-inter font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25"
            >
              Scopri di più
            </button>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="animate-bounce">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
