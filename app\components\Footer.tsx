'use client';

import Image from 'next/image';
import { Mail, Phone, MapPin, Instagram, Facebook, Linkedin, Youtube } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-dark-bg border-t border-border-gray">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <Image
              src="/logo/logo.PNG"
              alt="36k Agency Logo"
              width={150}
              height={50}
              className="h-12 w-auto"
            />
            <p className="font-inter text-text-secondary text-sm leading-relaxed">
              Agenzia creativa specializzata in social media management, content creation e strategie di marketing digitale.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="p-2 bg-gradient-primary rounded-lg text-white hover:scale-110 transition-transform duration-300"
                aria-label="Instagram"
              >
                <Instagram className="w-5 h-5" />
              </a>
              <a
                href="#"
                className="p-2 bg-gradient-primary rounded-lg text-white hover:scale-110 transition-transform duration-300"
                aria-label="Facebook"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a
                href="#"
                className="p-2 bg-gradient-primary rounded-lg text-white hover:scale-110 transition-transform duration-300"
                aria-label="LinkedIn"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a
                href="#"
                className="p-2 bg-gradient-primary rounded-lg text-white hover:scale-110 transition-transform duration-300"
                aria-label="YouTube"
              >
                <Youtube className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-syne text-lg font-semibold text-white mb-4">
              Link Rapidi
            </h3>
            <ul className="space-y-2">
              {[
                { name: 'Home', href: '#home' },
                { name: 'Chi Siamo', href: '#about' },
                { name: 'Servizi', href: '#services' },
                { name: 'Portfolio', href: '#portfolio' },
                { name: 'Contatti', href: '#contact' },
              ].map((link) => (
                <li key={link.name}>
                  <button
                    onClick={() => scrollToSection(link.href)}
                    className="font-inter text-text-secondary hover:text-white transition-colors duration-300"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-syne text-lg font-semibold text-white mb-4">
              Servizi
            </h3>
            <ul className="space-y-2">
              {[
                'Marketing',
                'Content Creation',
                'ADS & SEO',
                'Website Development',
                'Event Photography',
                'Graphics & Branding',
              ].map((service) => (
                <li key={service}>
                  <span className="font-inter text-text-secondary">
                    {service}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-syne text-lg font-semibold text-white mb-4">
              Contatti
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="w-4 h-4 text-purple-400" />
                <span className="font-inter text-text-secondary text-sm">
                  <EMAIL>
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-4 h-4 text-purple-400" />
                <span className="font-inter text-text-secondary text-sm">
                  +39 ************
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="w-4 h-4 text-purple-400" />
                <span className="font-inter text-text-secondary text-sm">
                  Roma, Italia
                </span>
              </div>
            </div>
          </div>
        </div>


      </div>

      {/* Copyright Bar */}
      <div className="bg-soft-black border-t border-border-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="font-inter text-text-secondary text-sm">
              © {currentYear} 36k Agency. Tutti i diritti riservati.
            </p>
            <div className="flex space-x-6 mt-2 md:mt-0">
              <a href="#" className="font-inter text-text-secondary hover:text-white text-sm transition-colors duration-300">
                Privacy Policy
              </a>
              <a href="#" className="font-inter text-text-secondary hover:text-white text-sm transition-colors duration-300">
                Termini di Servizio
              </a>
              <a href="#" className="font-inter text-text-secondary hover:text-white text-sm transition-colors duration-300">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
