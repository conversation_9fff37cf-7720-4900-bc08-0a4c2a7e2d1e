import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, phone, company, message, consent, privacyConsent } = body;

    // Validation
    if (!name || !email || !phone || !message || !privacyConsent) {
      return NextResponse.json(
        { error: 'Tutti i campi obbligatori devono essere compilati e il consenso alla privacy deve essere accettato' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Formato email non valido' },
        { status: 400 }
      );
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Email content
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background: linear-gradient(-45deg, #A020F0, #00E5FF); padding: 20px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 24px;">Nuovo Messaggio da 36k Agency</h1>
        </div>
        
        <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h2 style="color: #333; margin-bottom: 20px;">Dettagli del Contatto</h2>
          
          <div style="margin-bottom: 15px;">
            <strong style="color: #A020F0;">Nome:</strong>
            <span style="margin-left: 10px; color: #333;">${name}</span>
          </div>
          
          <div style="margin-bottom: 15px;">
            <strong style="color: #A020F0;">Email:</strong>
            <span style="margin-left: 10px; color: #333;">${email}</span>
          </div>
          
          <div style="margin-bottom: 15px;">
            <strong style="color: #A020F0;">Telefono:</strong>
            <span style="margin-left: 10px; color: #333;">${phone}</span>
          </div>
          
          ${company ? `
          <div style="margin-bottom: 15px;">
            <strong style="color: #A020F0;">Azienda:</strong>
            <span style="margin-left: 10px; color: #333;">${company}</span>
          </div>
          ` : ''}
          
          <div style="margin-bottom: 20px;">
            <strong style="color: #A020F0;">Messaggio:</strong>
            <div style="margin-top: 10px; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #A020F0; border-radius: 5px;">
              ${message.replace(/\n/g, '<br>')}
            </div>
          </div>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
            <p>Questo messaggio è stato inviato dal form di contatto del sito web 36k Agency.</p>
            <p>Data e ora: ${new Date().toLocaleString('it-IT')}</p>
          </div>
        </div>
      </div>
    `;

    // Send email
    await transporter.sendMail({
      from: `"${name}" <${process.env.EMAIL_FROM}>`,
      to: process.env.EMAIL_TO,
      replyTo: email,
      subject: `Nuovo messaggio da ${name} - 36k Agency`,
      html: htmlContent,
      text: `
        Nuovo messaggio da 36k Agency
        
        Nome: ${name}
        Email: ${email}
        Telefono: ${phone}
        ${company ? `Azienda: ${company}` : ''}
        
        Messaggio:
        ${message}
        
        Data e ora: ${new Date().toLocaleString('it-IT')}
      `,
    });

    return NextResponse.json(
      { message: 'Email inviata con successo' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Errore invio email:', error);
    return NextResponse.json(
      { error: 'Errore interno del server. Riprova più tardi.' },
      { status: 500 }
    );
  }
}
