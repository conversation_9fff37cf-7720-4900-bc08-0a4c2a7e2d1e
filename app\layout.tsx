import type { Metadata } from "next";
import { Source_Serif_4 } from "next/font/google";
import Script from "next/script";
import "./globals.css";
import CookieConsent from "../components/ui/CookieConsent";
import WhatsAppButton from "../components/ui/WhatsAppButton";

const sourceSerif = Source_Serif_4({
  subsets: ["latin"],
  variable: "--font-source-serif",
  display: "swap", 
});

export const metadata: Metadata = {
  title: "Ottica GR1 - Occhiali da Vista, da Sole e Lenti a Contatto",
  description: "Ottica GR1 a Montesacro dal 1982. Occhiali da vista, da sole, lenti a contatto e controllo vista. Tradizione, qualità e innovazione per la tua salute visiva.",
  keywords: "ottica, occhiali da vista, occhiali da sole, lenti a contatto, controllo vista, Montesacro, Roma",
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon.ico', sizes: 'any' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'android-chrome-192x192', url: '/android-chrome-192x192.png' },
      { rel: 'android-chrome-512x512', url: '/android-chrome-512x512.png' },
    ],
  },
  manifest: '/site.webmanifest',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="it" className={sourceSerif.variable}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="font-serif antialiased">
        {children}
        <CookieConsent />
        <WhatsAppButton />

        {/* TrustIndex Script */}
        <Script
          src="https://cdn.trustindex.io/loader.js"
          strategy="afterInteractive"
        />
      </body>
    </html>
  );
}
