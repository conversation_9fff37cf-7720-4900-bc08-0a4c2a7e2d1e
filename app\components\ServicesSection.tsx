'use client';

import { useState } from 'react';
import { 
  TrendingUp, 
  Camera, 
  Search, 
  Code, 
  Calendar, 
  Palette,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

const ServicesSection = () => {
  const [expandedService, setExpandedService] = useState<number | null>(null);

  const services = [
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: "Marketing",
      description: "Strategie di marketing innovative per far crescere il tuo brand",
      details: [
        "Project Management",
        "Brand Strategy", 
        "Piani di Comunicazione",
        "Content Marketing",
        "Consulenza Aziendale"
      ]
    },
    {
      icon: <Camera className="w-8 h-8" />,
      title: "Content Creation",
      description: "Contenuti creativi che catturano l'attenzione del tuo pubblico",
      details: [
        "Servizi Fotografici & Video Making",
        "Campagne Fotografiche",
        "Look Book",
        "Contenuti Social",
        "Produzione Video",
        "E-Commerce"
      ]
    },
    {
      icon: <Search className="w-8 h-8" />,
      title: "ADS & SEO",
      description: "Ottimizzazione e pubblicità per massimizzare la tua visibilità online",
      details: [
        "Google Ads",
        "Social Ads",
        "SEO On-Page/Off-Page",
        "LEAD Generation",
        "Keyword Research"
      ]
    },
    {
      icon: <Code className="w-8 h-8" />,
      title: "Website Development",
      description: "Siti web moderni e funzionali per la tua presenza digitale",
      details: [
        "Web Design",
        "Sviluppo Web",
        "E-Commerce",
        "Assistenza Sito web",
        "Landing Page"
      ]
    },
    {
      icon: <Calendar className="w-8 h-8" />,
      title: "Event Photography",
      description: "Documentazione professionale dei tuoi eventi più importanti",
      details: [
        "Pianificazione Eventi",
        "Promozione e PR",
        "Eventi Digitali",
        "Allestimenti e Scenografie",
        "Gestione Logistica"
      ]
    },
    {
      icon: <Palette className="w-8 h-8" />,
      title: "Graphics, Logo & Branding",
      description: "Identità visiva distintiva per il tuo brand",
      details: [
        "Brand Identity",
        "Contenuti Social",
        "Company Profile",
        "Logo Design",
        "Illustrazioni"
      ]
    }
  ];

  const toggleService = (index: number) => {
    setExpandedService(expandedService === index ? null : index);
  };

  return (
    <section id="services" className="py-20 bg-soft-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-syne text-4xl md:text-5xl font-bold text-white mb-6">
            I Nostri Servizi
          </h2>
          <p className="font-inter text-lg md:text-xl text-text-secondary max-w-3xl mx-auto">
            Offriamo soluzioni complete per la crescita digitale del tuo business
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="bg-dark-bg border border-border-gray rounded-xl p-6 hover:border-purple-500 transition-all duration-300 group"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-gradient-primary rounded-lg text-white">
                    {service.icon}
                  </div>
                  <h3 className="font-syne text-xl font-semibold text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-500 group-hover:to-cyan-400 transition-all duration-300">
                    {service.title}
                  </h3>
                </div>
                <button
                  onClick={() => toggleService(index)}
                  className="text-text-secondary hover:text-white transition-colors duration-300"
                >
                  {expandedService === index ? (
                    <ChevronUp className="w-5 h-5" />
                  ) : (
                    <ChevronDown className="w-5 h-5" />
                  )}
                </button>
              </div>

              <p className="font-inter text-text-secondary mb-4">
                {service.description}
              </p>

              {expandedService === index && (
                <div className="animate-fade-in-up">
                  <ul className="space-y-2">
                    {service.details.map((detail, detailIndex) => (
                      <li
                        key={detailIndex}
                        className="font-inter text-sm text-text-secondary flex items-center"
                      >
                        <div className="w-2 h-2 bg-gradient-primary rounded-full mr-3"></div>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button
            onClick={() => {
              const element = document.querySelector('#contact');
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="gradient-button px-8 py-4 rounded-full font-inter font-semibold text-lg transition-all duration-300 hover:scale-105"
          >
            Richiedi un Preventivo
          </button>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
