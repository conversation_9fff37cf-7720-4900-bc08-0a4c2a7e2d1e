'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';

const AboutSection = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  // Carousel animation states
  const [translateX1, setTranslateX1] = useState(0);
  const [translateX2, setTranslateX2] = useState(0);
  const animationRef1 = useRef<number | null>(null);
  const animationRef2 = useRef<number | null>(null);

  // List of shooting images
  const shootingImages = [
    '/img/Foto/Shooting/output/IMG_3330.webp',
    '/img/Foto/Shooting/output/IMG_6493_TIF.webp',
    '/img/Foto/Shooting/output/IMG_6514_TIF.webp',
    '/img/Foto/Shooting/output/IMG_6532_TIF.webp',
    '/img/Foto/Shooting/output/_DSC0189.webp',
    '/img/Foto/Shooting/output/_DSC0405.webp',
    '/img/Foto/Shooting/output/_DSC1512.webp',
    '/img/Foto/Shooting/output/_DSC1601.webp',
    '/img/Foto/Shooting/output/_DSC1633.webp',
    '/img/Foto/Shooting/output/_DSC1674.webp',
    '/img/Foto/Shooting/output/_DSC1795.webp',
    '/img/Foto/Shooting/output/_DSC2040.webp',
    '/img/Foto/Shooting/output/_DSC2061.webp',
    '/img/Foto/Shooting/output/_DSC2182.webp',
    '/img/Foto/Shooting/output/_DSC3028.webp',
    '/img/Foto/Shooting/output/_DSC3363.webp',
    '/img/Foto/Shooting/output/_DSC5102.webp',
    '/img/Foto/Shooting/output/_DSC5976.webp',
    '/img/Foto/Shooting/output/_DSC8073.webp',
    '/img/Foto/Shooting/output/_DSC8101.webp',
    '/img/Foto/Shooting/output/_DSC9661.webp',
    '/img/Foto/Shooting/output/_DSC9770.webp',
    '/img/Foto/Shooting/output/_DSC9890.webp',
    '/img/Foto/Shooting/output/sara-04.webp',
    '/img/Foto/Shooting/output/sara-06.webp',
    '/img/Foto/Shooting/output/sara-26.webp',
    '/img/Foto/Shooting/output/sara-28.webp',
  ];

  const eventiImages = [
    '/img/Foto/Eventi/output/_DSC2145.webp',
    '/img/Foto/Eventi/output/_DSC2182.webp',
    '/img/Foto/Eventi/output/_DSC3550.webp',
    '/img/Foto/Eventi/output/_DSC3619.webp',
    '/img/Foto/Eventi/output/_DSC4459.webp',
    '/img/Foto/Eventi/output/_DSC4495.webp',
    '/img/Foto/Eventi/output/_DSC4881.webp',
    '/img/Foto/Eventi/output/_DSC5007.webp',
    '/img/Foto/Eventi/output/_DSC6753.webp',
    '/img/Foto/Eventi/output/_DSC6757.webp',
    '/img/Foto/Eventi/output/_DSC7229.webp',
    '/img/Foto/Eventi/output/_DSC7961.webp',
    '/img/Foto/Eventi/output/_DSC7972.webp',
    '/img/Foto/Eventi/output/_DSC8043.webp',
    '/img/Foto/Eventi/output/_DSC9145.webp',
    '/img/Foto/Eventi/output/_DSC9818.webp',
    '/img/Foto/Eventi/output/DSC09617.webp',
    '/img/Foto/Eventi/output/IMG_1163.webp',
    '/img/Foto/Eventi/output/IMG_1166.webp',
    '/img/Foto/Eventi/output/IMG_1169.webp',
    '/img/Foto/Eventi/output/IMG_4116.webp',
    '/img/Foto/Eventi/output/IMG_4130.webp',
    '/img/Foto/Eventi/output/IMG_6691.webp',
  ];

  // Split images for two carousels
  const carousel1Images = [...shootingImages, ...shootingImages]; // Duplicate for infinite scroll
  const carousel2Images = [...eventiImages, ...eventiImages]; // Duplicate for infinite scroll

  // Animation logic for carousel 1 (moves left)
  useEffect(() => {
    const animate1 = () => {
      if (!isPaused) {
        setTranslateX1(prev => {
          const newValue = prev - 1;
          if (Math.abs(newValue) >= (shootingImages.length * 280)) {
            return 0;
          }
          return newValue;
        });
      }
      animationRef1.current = requestAnimationFrame(animate1);
    };

    animationRef1.current = requestAnimationFrame(animate1);
    return () => {
      if (animationRef1.current) {
        cancelAnimationFrame(animationRef1.current);
      }
    };
  }, [isPaused, shootingImages.length]);

  // Animation logic for carousel 2 (moves right, slower speed)
  useEffect(() => {
    const animate2 = () => {
      if (!isPaused) {
        setTranslateX2(prev => {
          const newValue = prev + 0.7; // Slower and opposite direction
          if (newValue >= (eventiImages.length * 280)) {
            return 0;
          }
          return newValue;
        });
      }
      animationRef2.current = requestAnimationFrame(animate2);
    };

    animationRef2.current = requestAnimationFrame(animate2);
    return () => {
      if (animationRef2.current) {
        cancelAnimationFrame(animationRef2.current);
      }
    };
  }, [isPaused, eventiImages.length]);

  const openModal = (imageSrc: string, index: number) => {
    setSelectedImage(imageSrc);
    setCurrentImageIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const nextImage = () => {
    const nextIndex = (currentImageIndex + 1) % shootingImages.length;
    setCurrentImageIndex(nextIndex);
    setSelectedImage(shootingImages[nextIndex]);
  };

  const prevImage = () => {
    const prevIndex = currentImageIndex === 0 ? shootingImages.length - 1 : currentImageIndex - 1;
    setCurrentImageIndex(prevIndex);
    setSelectedImage(shootingImages[prevIndex]);
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (selectedImage) {
        if (e.key === 'Escape') closeModal();
        if (e.key === 'ArrowRight') nextImage();
        if (e.key === 'ArrowLeft') prevImage();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedImage, currentImageIndex]);

  return (
    <section id="about" className="py-20 bg-soft-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-syne text-4xl md:text-5xl font-bold text-white mb-6">
            Chi Siamo
          </h2>
          <div className="max-w-4xl mx-auto">
            <p className="font-inter text-lg md:text-xl text-text-secondary leading-relaxed">
              36k Agency è un'agenzia creativa specializzata in social media Management, content creazione, 
              strategie di marketing e creazione di siti web e app. Ogni mese realizziamo centinaia di reel, 
              shooting fotografici, contenuti TikTok e campagne per brand, locali ed eventi in tutta Italia. 
              Lavoriamo con precisione, velocità e visione strategica, grazie a un team esperto. 
              Dall'idea iniziale alla pubblicazione, trasformiamo i contenuti in risultati.
            </p>
          </div>
        </div>

        {/* Dual Horizontal Carousels */}
        <div className="space-y-8">
          {/* First Carousel - Moving Left */}
          <div
            className="relative overflow-hidden"
            onMouseEnter={() => setIsPaused(true)}
            onMouseLeave={() => setIsPaused(false)}
          >
            <div
              className="flex space-x-6 transition-transform duration-75 ease-linear"
              style={{
                transform: `translateX(${translateX1}px)`,
              }}
            >
              {carousel1Images.map((image, index) => (
                <div
                  key={`carousel1-${index}`}
                  className="flex-shrink-0 w-64 h-64 md:w-72 md:h-72 relative cursor-pointer group"
                  onClick={() => openModal(image, index % shootingImages.length)}
                >
                  <div className="relative w-full h-full overflow-hidden rounded-xl shadow-lg group-hover:shadow-2xl group-hover:shadow-purple-500/20 transition-all duration-300">
                    <Image
                      src={image}
                      alt={`Shooting ${(index % shootingImages.length) + 1}`}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-110"
                      sizes="(max-width: 768px) 256px, 288px"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300"></div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                  </div>
                </div>
              ))}
            </div>

            {/* Gradient overlays */}
            <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-soft-black to-transparent pointer-events-none z-10"></div>
            <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-soft-black to-transparent pointer-events-none z-10"></div>
          </div>

          {/* Second Carousel - Moving Right */}
          <div
            className="relative overflow-hidden"
            onMouseEnter={() => setIsPaused(true)}
            onMouseLeave={() => setIsPaused(false)}
          >
            <div
              className="flex space-x-6 transition-transform duration-75 ease-linear"
              style={{
                transform: `translateX(${translateX2}px)`,
              }}
            >
              {carousel2Images.map((image, index) => (
                <div
                  key={`carousel2-${index}`}
                  className="flex-shrink-0 w-64 h-64 md:w-72 md:h-72 relative cursor-pointer group"
                  onClick={() => openModal(image, index % eventiImages.length)}
                >
                  <div className="relative w-full h-full overflow-hidden rounded-xl shadow-lg group-hover:shadow-2xl group-hover:shadow-purple-500/20 transition-all duration-300">
                    <Image
                      src={image}
                      alt={`Eventi ${(index % eventiImages.length) + 1}`}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-110"
                      sizes="(max-width: 768px) 256px, 288px"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300"></div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                  </div>
                </div>
              ))}
            </div>

            {/* Gradient overlays */}
            <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-soft-black to-transparent pointer-events-none z-10"></div>
            <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-soft-black to-transparent pointer-events-none z-10"></div>
          </div>
        </div>
      </div>

      {/* Fullscreen Modal */}
      {selectedImage && (
        <div className="fixed inset-0 z-50 modal-overlay flex items-center justify-center p-4">
          <div className="relative max-w-7xl max-h-full">
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all duration-300"
            >
              <X size={24} />
            </button>
            
            <button
              onClick={prevImage}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all duration-300"
            >
              <ChevronLeft size={24} />
            </button>
            
            <button
              onClick={nextImage}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all duration-300"
            >
              <ChevronRight size={24} />
            </button>

            <Image
              src={selectedImage}
              alt="Fullscreen view"
              width={1200}
              height={800}
              className="max-w-full max-h-full object-contain"
              priority
            />
          </div>
        </div>
      )}
    </section>
  );
};

export default AboutSection;
