'use client';

import Image from 'next/image';
import { Camera, Palette, Building, Share2 } from 'lucide-react';

const ServicesShowcase = () => {
  const services = [
    {
      id: 1,
      title: "Shooting per Eventi",
      description: "Catturiamo i momenti più importanti dei tuoi eventi con professionalità e creatività. Dalla pianificazione alla post-produzione, offriamo un servizio fotografico completo per matrimoni, eventi aziendali, feste private e manifestazioni pubbliche.",
      features: [
        "Fotografia professionale per eventi",
        "Servizio completo dalla pianificazione alla consegna",
        "Post-produzione e ritocco professionale",
        "Consegna rapida delle immagini"
      ],
      image: "/img/Foto/Eventi/output/_DSC7229.webp",
      icon: <Camera className="w-8 h-8" />,
      reverse: false
    },
    {
      id: 2,
      title: "Grafiche per Locandine e Brochure",
      description: "Creiamo materiali grafici di impatto che comunicano efficacemente il tuo messaggio. Dalle locandine promozionali alle brochure aziendali, ogni progetto è studiato per catturare l'attenzione e trasmettere professionalità.",
      features: [
        "Design personalizzato per ogni progetto",
        "Formati ottimizzati per stampa e digitale",
        "Revisioni illimitate fino alla soddisfazione",
        "Consegna in tutti i formati richiesti"
      ],
      image: "/img/Grafiche/output/IMG_8763.webp",
      icon: <Palette className="w-8 h-8" />,
      reverse: true
    },
    {
      id: 3,
      title: "Branding Aziendale",
      description: "Sviluppiamo identità visive complete che distinguono il tuo brand dalla concorrenza. Dal logo al manuale di brand identity, creiamo un sistema coerente che rafforza la tua presenza sul mercato e aumenta il riconoscimento del brand.",
      features: [
        "Sviluppo logo e identità visiva",
        "Manuale di brand identity completo",
        "Applicazioni su tutti i supporti",
        "Consulenza strategica per il posizionamento"
      ],
      image: "/img/Grafiche/output/MOULINROUGE.webp",
      icon: <Building className="w-8 h-8" />,
      reverse: false
    },
    {
      id: 4,
      title: "Pubblicità Social",
      description: "Gestiamo le tue campagne pubblicitarie sui social media per massimizzare il ROI e raggiungere il tuo target ideale. Dalla strategia alla creatività, fino all'analisi dei risultati, offriamo un servizio completo di social media advertising.",
      features: [
        "Strategia pubblicitaria personalizzata",
        "Gestione campagne Facebook e Instagram Ads",
        "Creatività ottimizzate per ogni piattaforma",
        "Report dettagliati e ottimizzazione continua"
      ],
      image: "/img/Grafiche/output/IMG_9650.webp",
      icon: <Share2 className="w-8 h-8" />,
      reverse: true
    }
  ];

  return (
    <section id="services-showcase" className="py-20 bg-soft-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-syne text-4xl md:text-5xl font-bold text-white mb-6">
            I Nostri Servizi Principali
          </h2>
          <p className="font-inter text-lg md:text-xl text-text-secondary max-w-3xl mx-auto">
            Scopri come possiamo aiutare la tua azienda a crescere con i nostri servizi specializzati
          </p>
        </div>

        <div className="space-y-20">
          {services.map((service) => (
            <div
              key={service.id}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                service.reverse ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Text Content */}
              <div className={`space-y-6 ${service.reverse ? 'lg:col-start-2' : ''}`}>
                <div className="flex items-center space-x-4 mb-6">
                  <div className="p-3 bg-gradient-primary rounded-lg">
                    {service.icon}
                  </div>
                  <h3 className="font-syne text-2xl md:text-3xl font-bold text-white">
                    {service.title}
                  </h3>
                </div>

                <p className="font-inter text-lg text-text-secondary leading-relaxed">
                  {service.description}
                </p>

                <div className="space-y-3">
                  {service.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-gradient-primary rounded-full flex-shrink-0"></div>
                      <span className="font-inter text-text-secondary">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="pt-6">
                  <button
                    onClick={() => {
                      const element = document.querySelector('#contact');
                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth' });
                      }
                    }}
                    className="bg-gradient-primary text-white px-8 py-4 rounded-lg font-inter font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25"
                  >
                    Richiedi Informazioni
                  </button>
                </div>
              </div>

              {/* Image */}
              <div className={`relative ${service.reverse ? 'lg:col-start-1' : ''}`}>
                <div className="relative aspect-square lg:aspect-[4/3] overflow-hidden rounded-xl border-2 border-border-gray hover:border-purple-500 transition-colors duration-300 group">
                  <Image
                    src={service.image}
                    alt={service.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 600px"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
                  
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  
                  {/* Service number */}
                  <div className="absolute top-4 right-4 w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center">
                    <span className="font-syne text-white font-bold text-lg">
                      {service.id.toString().padStart(2, '0')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16 pt-16 border-t border-border-gray">
          <h3 className="font-syne text-2xl md:text-3xl font-bold text-white mb-4">
            Pronto a far crescere il tuo business?
          </h3>
          <p className="font-inter text-lg text-text-secondary mb-8 max-w-2xl mx-auto">
            Contattaci per una consulenza gratuita e scopri come possiamo aiutarti a raggiungere i tuoi obiettivi
          </p>
          <button
            onClick={() => {
              const element = document.querySelector('#contact');
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="bg-gradient-primary text-white px-10 py-4 rounded-full font-inter font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25"
          >
            Inizia Ora
          </button>
        </div>
      </div>
    </section>
  );
};

export default ServicesShowcase;
