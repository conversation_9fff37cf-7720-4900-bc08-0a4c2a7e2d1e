declare global {
  interface Window {
    gtag: (
      command: 'config' | 'consent' | 'event' | 'js' | 'set',
      targetId: string | Date | 'default' | 'update',
      config?: {
        [key: string]: any;
        analytics_storage?: 'granted' | 'denied';
        ad_storage?: 'granted' | 'denied';
        ad_user_data?: 'granted' | 'denied';
        ad_personalization?: 'granted' | 'denied';
        functionality_storage?: 'granted' | 'denied';
        security_storage?: 'granted' | 'denied';
        wait_for_update?: number;
        page_title?: string;
        page_location?: string;
      }
    ) => void;
    dataLayer: any[];
  }
}

export {};
