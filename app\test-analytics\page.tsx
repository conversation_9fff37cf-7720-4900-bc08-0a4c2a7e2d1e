'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Play, Download, ExternalLink, Image as ImageIcon, MousePointer, BarChart3 } from 'lucide-react';
import { 
  trackEvent, 
  trackFormSubmission, 
  trackNavigationClick, 
  trackCTAClick, 
  trackImageClick, 
  trackExternalLinkClick,
  trackVideoPlay,
  trackDownload,
  trackSocialShare,
  isAnalyticsEnabled
} from '../utils/analytics';

export default function TestAnalyticsPage() {
  const [analyticsStatus, setAnalyticsStatus] = useState<boolean | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const checkAnalyticsStatus = () => {
    const enabled = isAnalyticsEnabled();
    setAnalyticsStatus(enabled);
    addTestResult(`Analytics Status: ${enabled ? 'ENABLED' : 'DISABLED'}`);
  };

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testScrollTracking = () => {
    // Simulate scroll to bottom
    window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    addTestResult('Scroll tracking test initiated - scroll to bottom');
  };

  const testFormSubmission = () => {
    trackFormSubmission('test_form', true);
    addTestResult('Form submission event tracked (success)');
  };

  const testNavigationClick = () => {
    trackNavigationClick('Test Navigation', '#test');
    addTestResult('Navigation click event tracked');
  };

  const testCTAClick = () => {
    trackCTAClick('Test CTA Button', window.location.href);
    addTestResult('CTA click event tracked');
  };

  const testImageClick = () => {
    trackImageClick('Test Image Alt Text', '/test-image.jpg', 'test_image');
    addTestResult('Image click event tracked');
  };

  const testExternalLink = () => {
    trackExternalLinkClick('https://google.com', 'Google Link');
    addTestResult('External link click event tracked');
  };

  const testVideoPlay = () => {
    trackVideoPlay('Test Video', '/test-video.mp4');
    addTestResult('Video play event tracked');
  };

  const testDownload = () => {
    trackDownload('test-file.pdf', 'pdf');
    addTestResult('File download event tracked');
  };

  const testSocialShare = () => {
    trackSocialShare('instagram', window.location.href);
    addTestResult('Social share event tracked');
  };

  const testCustomEvent = () => {
    trackEvent('custom_test_event', {
      category: 'test',
      label: 'Custom Test Event',
      value: 1,
      custom_parameter: 'test_value'
    });
    addTestResult('Custom event tracked');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-soft-black text-white">
      {/* Header */}
      <header className="bg-dark-bg border-b border-border-gray">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-4">
            <Link 
              href="/"
              className="flex items-center space-x-2 text-purple-400 hover:text-purple-300 transition-colors duration-300"
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="font-inter">Torna al sito</span>
            </Link>
          </div>
          <div className="mt-4">
            <div className="flex items-center space-x-3 mb-2">
              <BarChart3 className="w-8 h-8 text-purple-500" />
              <h1 className="font-syne text-3xl font-bold text-white">Test Analytics</h1>
            </div>
            <p className="font-inter text-text-secondary">
              Pagina di test per verificare il funzionamento del tracking GA4
            </p>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Analytics Status */}
        <section className="mb-12">
          <div className="bg-dark-bg border border-border-gray rounded-xl p-8">
            <h2 className="font-syne text-2xl font-semibold text-white mb-6">Status Analytics</h2>
            <div className="flex items-center space-x-4 mb-4">
              <button
                onClick={checkAnalyticsStatus}
                className="px-4 py-2 bg-gradient-primary rounded-lg text-white hover:scale-105 transition-all duration-300 font-inter"
              >
                Controlla Status
              </button>
              {analyticsStatus !== null && (
                <div className={`px-3 py-1 rounded-full text-sm font-semibold ${
                  analyticsStatus 
                    ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                    : 'bg-red-500/20 text-red-400 border border-red-500/30'
                }`}>
                  {analyticsStatus ? 'ANALYTICS ATTIVO' : 'ANALYTICS DISATTIVO'}
                </div>
              )}
            </div>
            <p className="font-inter text-text-secondary text-sm">
              {analyticsStatus === false && 
                'Per testare gli eventi, accetta i cookie analitici nel banner dei cookie.'
              }
              {analyticsStatus === true && 
                'Gli eventi verranno inviati a Google Analytics. Controlla la console del browser per i log.'
              }
            </p>
          </div>
        </section>

        {/* Test Buttons */}
        <section className="mb-12">
          <div className="bg-dark-bg border border-border-gray rounded-xl p-8">
            <h2 className="font-syne text-2xl font-semibold text-white mb-6">Test Eventi</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              
              <button
                onClick={testScrollTracking}
                className="flex items-center space-x-2 p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg hover:bg-purple-500/20 transition-all duration-300"
              >
                <MousePointer className="w-5 h-5 text-purple-400" />
                <span className="font-inter text-white">Test Scroll</span>
              </button>

              <button
                onClick={testFormSubmission}
                className="flex items-center space-x-2 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg hover:bg-blue-500/20 transition-all duration-300"
              >
                <Play className="w-5 h-5 text-blue-400" />
                <span className="font-inter text-white">Test Form</span>
              </button>

              <button
                onClick={testNavigationClick}
                className="flex items-center space-x-2 p-4 bg-green-500/10 border border-green-500/20 rounded-lg hover:bg-green-500/20 transition-all duration-300"
              >
                <ArrowLeft className="w-5 h-5 text-green-400" />
                <span className="font-inter text-white">Test Navigation</span>
              </button>

              <button
                onClick={testCTAClick}
                className="flex items-center space-x-2 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg hover:bg-yellow-500/20 transition-all duration-300"
              >
                <MousePointer className="w-5 h-5 text-yellow-400" />
                <span className="font-inter text-white">Test CTA</span>
              </button>

              <button
                onClick={testImageClick}
                className="flex items-center space-x-2 p-4 bg-pink-500/10 border border-pink-500/20 rounded-lg hover:bg-pink-500/20 transition-all duration-300"
              >
                <ImageIcon className="w-5 h-5 text-pink-400" />
                <span className="font-inter text-white">Test Image</span>
              </button>

              <button
                onClick={testExternalLink}
                className="flex items-center space-x-2 p-4 bg-cyan-500/10 border border-cyan-500/20 rounded-lg hover:bg-cyan-500/20 transition-all duration-300"
              >
                <ExternalLink className="w-5 h-5 text-cyan-400" />
                <span className="font-inter text-white">Test External</span>
              </button>

              <button
                onClick={testVideoPlay}
                className="flex items-center space-x-2 p-4 bg-red-500/10 border border-red-500/20 rounded-lg hover:bg-red-500/20 transition-all duration-300"
              >
                <Play className="w-5 h-5 text-red-400" />
                <span className="font-inter text-white">Test Video</span>
              </button>

              <button
                onClick={testDownload}
                className="flex items-center space-x-2 p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg hover:bg-orange-500/20 transition-all duration-300"
              >
                <Download className="w-5 h-5 text-orange-400" />
                <span className="font-inter text-white">Test Download</span>
              </button>

              <button
                onClick={testSocialShare}
                className="flex items-center space-x-2 p-4 bg-indigo-500/10 border border-indigo-500/20 rounded-lg hover:bg-indigo-500/20 transition-all duration-300"
              >
                <ExternalLink className="w-5 h-5 text-indigo-400" />
                <span className="font-inter text-white">Test Social</span>
              </button>

              <button
                onClick={testCustomEvent}
                className="flex items-center space-x-2 p-4 bg-gradient-to-r from-purple-500/10 to-cyan-500/10 border border-purple-500/20 rounded-lg hover:from-purple-500/20 hover:to-cyan-500/20 transition-all duration-300"
              >
                <BarChart3 className="w-5 h-5 text-purple-400" />
                <span className="font-inter text-white">Test Custom</span>
              </button>

            </div>
          </div>
        </section>

        {/* Test Results */}
        <section className="mb-12">
          <div className="bg-dark-bg border border-border-gray rounded-xl p-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="font-syne text-2xl font-semibold text-white">Risultati Test</h2>
              <button
                onClick={clearResults}
                className="px-4 py-2 bg-red-500/20 border border-red-500/30 text-red-400 rounded-lg hover:bg-red-500/30 transition-all duration-300 font-inter text-sm"
              >
                Pulisci
              </button>
            </div>
            <div className="bg-soft-black border border-border-gray rounded-lg p-4 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="font-inter text-text-secondary text-sm">Nessun test eseguito ancora...</p>
              ) : (
                <div className="space-y-2">
                  {testResults.map((result, index) => (
                    <div key={index} className="font-mono text-sm text-green-400">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Instructions */}
        <section className="mb-12">
          <div className="bg-gradient-to-r from-purple-500/20 to-cyan-500/20 border border-purple-500/30 rounded-xl p-8">
            <h2 className="font-syne text-2xl font-semibold text-white mb-6">Istruzioni</h2>
            <div className="space-y-4 font-inter text-text-secondary">
              <div>
                <h3 className="font-semibold text-white mb-2">1. Verifica Consenso Cookie</h3>
                <p className="text-sm">Assicurati di aver accettato i cookie analitici nel banner dei cookie.</p>
              </div>
              <div>
                <h3 className="font-semibold text-white mb-2">2. Apri Console Browser</h3>
                <p className="text-sm">Premi F12 e vai nella tab Console per vedere i log degli eventi.</p>
              </div>
              <div>
                <h3 className="font-semibold text-white mb-2">3. Testa Eventi</h3>
                <p className="text-sm">Clicca sui pulsanti di test per inviare eventi a Google Analytics.</p>
              </div>
              <div>
                <h3 className="font-semibold text-white mb-2">4. Verifica in GA4</h3>
                <p className="text-sm">Controlla in Google Analytics 4 (Real-time reports) se gli eventi arrivano.</p>
              </div>
            </div>
          </div>
        </section>

      </main>
    </div>
  );
}
