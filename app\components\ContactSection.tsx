'use client';

import { useState } from 'react';
import { Mail, Phone, MapPin, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { trackFormSubmission } from '../utils/analytics';
import Link from 'next/link';

const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    message: '',
    consent: false,
    privacyConsent: false, // New privacy policy consent
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.email || !formData.phone || !formData.message || !formData.privacyConsent) {
      setSubmitStatus('error');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        // Track successful form submission
        trackFormSubmission('contact_form', true);
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          message: '',
          consent: false,
          privacyConsent: false,
        });
      } else {
        console.error('Errore invio:', result.error);
        setSubmitStatus('error');
        // Track failed form submission
        trackFormSubmission('contact_form', false);
      }
    } catch (error) {
      console.error('Errore invio email:', error);
      setSubmitStatus('error');
      // Track failed form submission
      trackFormSubmission('contact_form', false);
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus('idle'), 5000);
    }
  };

  return (
    <section id="contact" className="py-20 bg-soft-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-syne text-4xl md:text-5xl font-bold text-white mb-6">
            Contattaci
          </h2>
          <p className="font-inter text-lg md:text-xl text-text-secondary max-w-3xl mx-auto">
            Pronto a far crescere il tuo business? Contattaci per una consulenza gratuita
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="font-syne text-2xl font-semibold text-white mb-6">
                Informazioni di Contatto
              </h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-gradient-primary rounded-lg">
                    <Mail className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="font-inter text-white font-medium">Email</p>
                    <p className="font-inter text-text-secondary"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-gradient-primary rounded-lg">
                    <Phone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="font-inter text-white font-medium">Telefono</p>
                    <p className="font-inter text-text-secondary">+39 ************</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-gradient-primary rounded-lg">
                    <MapPin className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="font-inter text-white font-medium">Sede</p>
                    <p className="font-inter text-text-secondary">Via Val Maggia 28 Roma, Italia</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-dark-bg border border-border-gray rounded-xl p-6">
              <h4 className="font-syne text-xl font-semibold text-white mb-4">
                Orari di Lavoro
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-inter text-text-secondary">Lunedì - Venerdì</span>
                  <span className="font-inter text-white">9:00 - 18:00</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-inter text-text-secondary">Sabato</span>
                  <span className="font-inter text-white">9:00 - 13:00</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-inter text-text-secondary">Domenica</span>
                  <span className="font-inter text-white">Chiuso</span>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-dark-bg border border-border-gray rounded-xl p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="gradient-border">
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Nome *"
                    className="w-full px-4 py-3 bg-transparent text-white placeholder-text-secondary focus:outline-none"
                    required
                  />
                </div>
                <div className="gradient-border">
                  <input
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder="Azienda"
                    className="w-full px-4 py-3 bg-transparent text-white placeholder-text-secondary focus:outline-none"
                  />
                </div>
              </div>

              <div className="gradient-border">
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Email *"
                  className="w-full px-4 py-3 bg-transparent text-white placeholder-text-secondary focus:outline-none"
                  required
                />
              </div>

              <div className="gradient-border">
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="Telefono *"
                  className="w-full px-4 py-3 bg-transparent text-white placeholder-text-secondary focus:outline-none"
                  required
                />
              </div>

              <div className="gradient-border">
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Messaggio"
                  rows={4}
                  className="w-full px-4 py-3 bg-transparent text-white placeholder-text-secondary focus:outline-none resize-none"
                />
              </div>

              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  name="privacyConsent"
                  checked={formData.privacyConsent}
                  onChange={handleInputChange}
                  className="mt-1 w-4 h-4 text-purple-500 bg-transparent border-border-gray rounded focus:ring-purple-500"
                  required
                />
                <label className="font-inter text-sm text-text-secondary">
                  Accetto il trattamento dei dati personali secondo la{' '}
                  <Link
                    href="/privacy-policy"
                    className="text-purple-400 hover:text-purple-300 underline transition-colors duration-300"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Privacy Policy
                  </Link>{' '}
                  *
                </label>
              </div>

              <button
                type="submit"
                disabled={isSubmitting || !formData.name || !formData.email || !formData.phone || !formData.message || !formData.privacyConsent}
                className="w-full gradient-button px-6 py-4 rounded-lg font-inter font-semibold text-lg transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex items-center justify-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    <span>Invio in corso...</span>
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5" />
                    <span>Invia Messaggio</span>
                  </>
                )}
              </button>

              {/* Status Messages */}
              {submitStatus === 'success' && (
                <div className="flex items-center space-x-2 text-green-400">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-inter">Messaggio inviato con successo!</span>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="flex items-center space-x-2 text-red-400">
                  <AlertCircle className="w-5 h-5" />
                  <span className="font-inter">Errore nell'invio. Riprova più tardi.</span>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
