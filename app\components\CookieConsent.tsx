'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, Shield, BarChart3, Target } from 'lucide-react';

interface ConsentState {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
}

const CookieConsent = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [consent, setConsent] = useState<ConsentState>({
    necessary: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
  });

  useEffect(() => {
    // Check if consent has already been given
    const savedConsent = localStorage.getItem('cookie-consent');
    if (!savedConsent) {
      setIsVisible(true);
    } else {
      const parsedConsent = JSON.parse(savedConsent);
      setConsent(parsedConsent);
      updateGoogleConsent(parsedConsent);
    }
  }, []);

  const updateGoogleConsent = (consentState: ConsentState) => {
    // Google Consent Mode v2 implementation
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': consentState.analytics ? 'granted' : 'denied',
        'ad_storage': consentState.marketing ? 'granted' : 'denied',
        'ad_user_data': consentState.marketing ? 'granted' : 'denied',
        'ad_personalization': consentState.marketing ? 'granted' : 'denied',
        'functionality_storage': 'granted',
        'security_storage': 'granted',
      });
    }
  };

  const handleAcceptAll = () => {
    const newConsent = {
      necessary: true,
      analytics: true,
      marketing: true,
    };
    setConsent(newConsent);
    saveConsent(newConsent);
  };

  const handleAcceptSelected = () => {
    saveConsent(consent);
  };

  const handleRejectAll = () => {
    const newConsent = {
      necessary: true,
      analytics: false,
      marketing: false,
    };
    setConsent(newConsent);
    saveConsent(newConsent);
  };

  const saveConsent = (consentState: ConsentState) => {
    localStorage.setItem('cookie-consent', JSON.stringify(consentState));
    localStorage.setItem('cookie-consent-date', new Date().toISOString());
    updateGoogleConsent(consentState);
    setIsVisible(false);
  };

  const handleConsentChange = (type: keyof ConsentState, value: boolean) => {
    if (type === 'necessary') return; // Cannot disable necessary cookies
    setConsent(prev => ({ ...prev, [type]: value }));
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center p-4 pointer-events-none">
      <div className="glass-effect rounded-xl max-w-4xl w-full pointer-events-auto border-2 border-gradient-primary">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-primary rounded-lg">
                <Cookie className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-syne text-xl font-semibold text-white">
                Gestione Cookie
              </h3>
            </div>
            <button
              onClick={() => setIsVisible(false)}
              className="text-text-secondary hover:text-white transition-colors duration-300"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Main content */}
          <div className="mb-6">
            <p className="font-inter text-text-secondary mb-4 leading-relaxed">
              Utilizziamo i cookie per migliorare la tua esperienza di navigazione, analizzare il traffico del sito 
              e personalizzare i contenuti. Puoi scegliere quali categorie di cookie accettare.
            </p>

            {showDetails && (
              <div className="space-y-4 mb-6">
                {/* Necessary Cookies */}
                <div className="bg-dark-bg rounded-lg p-4 border border-border-gray">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <Shield className="w-5 h-5 text-green-400" />
                      <span className="font-inter font-medium text-white">Cookie Necessari</span>
                    </div>
                    <div className="bg-green-500 rounded-full w-12 h-6 flex items-center justify-end px-1">
                      <div className="bg-white rounded-full w-4 h-4"></div>
                    </div>
                  </div>
                  <p className="font-inter text-sm text-text-secondary">
                    Questi cookie sono essenziali per il funzionamento del sito web e non possono essere disabilitati.
                  </p>
                </div>

                {/* Analytics Cookies */}
                <div className="bg-dark-bg rounded-lg p-4 border border-border-gray">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <BarChart3 className="w-5 h-5 text-blue-400" />
                      <span className="font-inter font-medium text-white">Cookie Analitici</span>
                    </div>
                    <button
                      onClick={() => handleConsentChange('analytics', !consent.analytics)}
                      className={`rounded-full w-12 h-6 flex items-center px-1 transition-colors duration-300 ${
                        consent.analytics ? 'bg-gradient-primary justify-end' : 'bg-gray-600 justify-start'
                      }`}
                    >
                      <div className="bg-white rounded-full w-4 h-4"></div>
                    </button>
                  </div>
                  <p className="font-inter text-sm text-text-secondary">
                    Ci aiutano a capire come i visitatori interagiscono con il sito raccogliendo informazioni anonime.
                  </p>
                </div>

                {/* Marketing Cookies */}
                <div className="bg-dark-bg rounded-lg p-4 border border-border-gray">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <Target className="w-5 h-5 text-purple-400" />
                      <span className="font-inter font-medium text-white">Cookie di Marketing</span>
                    </div>
                    <button
                      onClick={() => handleConsentChange('marketing', !consent.marketing)}
                      className={`rounded-full w-12 h-6 flex items-center px-1 transition-colors duration-300 ${
                        consent.marketing ? 'bg-gradient-primary justify-end' : 'bg-gray-600 justify-start'
                      }`}
                    >
                      <div className="bg-white rounded-full w-4 h-4"></div>
                    </button>
                  </div>
                  <p className="font-inter text-sm text-text-secondary">
                    Utilizzati per tracciare i visitatori sui siti web per mostrare annunci pertinenti e coinvolgenti.
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="px-4 py-2 border border-border-gray rounded-lg text-text-secondary hover:text-white hover:border-white transition-all duration-300 font-inter"
            >
              {showDetails ? 'Nascondi Dettagli' : 'Mostra Dettagli'}
            </button>
            
            <div className="flex flex-col sm:flex-row gap-3 sm:ml-auto">
              <button
                onClick={handleRejectAll}
                className="px-6 py-2 border border-border-gray rounded-lg text-white hover:bg-border-gray transition-all duration-300 font-inter"
              >
                Rifiuta Tutto
              </button>
              
              {showDetails && (
                <button
                  onClick={handleAcceptSelected}
                  className="px-6 py-2 bg-dark-bg border border-gradient-primary rounded-lg text-white hover:bg-gradient-primary transition-all duration-300 font-inter"
                >
                  Salva Preferenze
                </button>
              )}
              
              <button
                onClick={handleAcceptAll}
                className="px-6 py-2 bg-gradient-primary rounded-lg text-white hover:scale-105 transition-all duration-300 font-inter font-semibold"
              >
                Accetta Tutto
              </button>
            </div>
          </div>

          {/* Legal links */}
          <div className="mt-4 pt-4 border-t border-border-gray">
            <div className="flex flex-wrap gap-4 text-sm">
              <a href="#" className="text-text-secondary hover:text-white transition-colors duration-300 font-inter">
                Privacy Policy
              </a>
              <a href="#" className="text-text-secondary hover:text-white transition-colors duration-300 font-inter">
                Cookie Policy
              </a>
              <a href="#" className="text-text-secondary hover:text-white transition-colors duration-300 font-inter">
                Termini di Servizio
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
