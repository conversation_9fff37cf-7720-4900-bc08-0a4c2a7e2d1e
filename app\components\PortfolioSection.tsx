'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import { useDrag } from '@use-gesture/react';

const PortfolioSection = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [translateX, setTranslateX] = useState(0);
  const carouselRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();

  // Combined portfolio images from graphics and shooting
  const portfolioImages = [
    // Graphics
    '/img/Grafiche/output/0A19C122-79D6-47F7-B7EC-44F593BF595E.webp',
    '/img/Grafiche/output/0E6019FE-5F69-4EB9-803A-78E207B27004.webp',
    '/img/Grafiche/output/43FADF70-CC9C-4A6C-87CD-DBB5B2270D83.webp',
    '/img/Grafiche/output/C6B42CC7-136C-44A6-BCC3-7D9364207D96.webp',
    '/img/Grafiche/output/IMG_4121.webp',
    '/img/Grafiche/output/IMG_4930.webp',
    '/img/Grafiche/output/IMG_5060.webp',
    '/img/Grafiche/output/IMG_6708.webp',
    '/img/Grafiche/output/IMG_7854.webp',
    '/img/Grafiche/output/IMG_8010.webp',
    '/img/Grafiche/output/IMG_8763.webp',
    '/img/Grafiche/output/IMG_8768.webp',
    '/img/Grafiche/output/IMG_8771.webp',
    '/img/Grafiche/output/IMG_9650.webp',
    '/img/Grafiche/output/IMG_9736.webp',
    '/img/Grafiche/output/MOULINROUGE.webp',
    // Shooting photos
    '/img/Foto/Shooting/output/IMG_3330.webp',
    '/img/Foto/Shooting/output/IMG_6493_TIF.webp',
    '/img/Foto/Shooting/output/IMG_6514_TIF.webp',
    '/img/Foto/Shooting/output/_DSC0189.webp',
    '/img/Foto/Shooting/output/_DSC1512.webp',
    '/img/Foto/Shooting/output/_DSC1601.webp',
    '/img/Foto/Shooting/output/_DSC2040.webp',
    '/img/Foto/Shooting/output/_DSC3028.webp',
    '/img/Foto/Shooting/output/_DSC5102.webp',
    '/img/Foto/Shooting/output/_DSC8073.webp',
    '/img/Foto/Shooting/output/_DSC9661.webp',
    '/img/Foto/Shooting/output/sara-04.webp',
    '/img/Foto/Shooting/output/sara-26.webp',
  ];

  // Duplicate for infinite scroll
  const duplicatedImages = [...portfolioImages, ...portfolioImages, ...portfolioImages];

  // Animation logic
  useEffect(() => {
    const animate = () => {
      if (!isPaused) {
        setTranslateX(prev => {
          const newValue = prev - 0.8;
          // Reset when we've scrolled through one full set
          if (Math.abs(newValue) >= (portfolioImages.length * 400)) {
            return 0;
          }
          return newValue;
        });
      }
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isPaused, portfolioImages.length]);

  // Drag gesture handling
  const bind = useDrag(({ offset: [ox], velocity: [vx], direction: [dx], cancel }) => {
    if (Math.abs(vx) > 0.5) {
      setTranslateX(prev => {
        const newValue = prev + (dx > 0 ? 200 : -200);
        if (Math.abs(newValue) >= (portfolioImages.length * 400)) {
          return 0;
        }
        return Math.max(-portfolioImages.length * 400, Math.min(200, newValue));
      });
      cancel();
    } else {
      setTranslateX(prev => prev + ox * 0.5);
    }
  }, {
    axis: 'x',
    bounds: { left: -portfolioImages.length * 400, right: 200 },
    rubberband: true,
  });

  const openModal = (imageSrc: string, index: number) => {
    setSelectedImage(imageSrc);
    setCurrentImageIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const nextImage = () => {
    const nextIndex = (currentImageIndex + 1) % portfolioImages.length;
    setCurrentImageIndex(nextIndex);
    setSelectedImage(portfolioImages[nextIndex]);
  };

  const prevImage = () => {
    const prevIndex = currentImageIndex === 0 ? portfolioImages.length - 1 : currentImageIndex - 1;
    setCurrentImageIndex(prevIndex);
    setSelectedImage(portfolioImages[prevIndex]);
  };

  return (
    <section id="portfolio" className="py-20 bg-dark-bg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-syne text-4xl md:text-5xl font-bold text-white mb-6">
            Il Nostro Portfolio
          </h2>
          <p className="font-inter text-lg md:text-xl text-text-secondary max-w-3xl mx-auto">
            Una selezione dei nostri lavori più rappresentativi tra grafiche e shooting fotografici
          </p>
        </div>

        {/* Enhanced Auto-playing carousel */}
        <div
          className="relative overflow-hidden rounded-xl cursor-grab active:cursor-grabbing"
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
          onTouchStart={() => setIsPaused(true)}
          onTouchEnd={() => setIsPaused(false)}
        >
          <div
            ref={carouselRef}
            {...bind()}
            className="flex space-x-4 transition-transform duration-75 ease-linear"
            style={{
              transform: `translateX(${translateX}px)`,
              touchAction: 'pan-y pinch-zoom',
            }}
          >
            {duplicatedImages.map((image, index) => (
              <div
                key={index}
                className="flex-shrink-0 w-64 h-64 md:w-80 md:h-80 lg:w-96 lg:h-96 relative cursor-pointer group select-none"
                onClick={() => openModal(image, index % portfolioImages.length)}
              >
                <Image
                  src={image}
                  alt={`Portfolio ${(index % portfolioImages.length) + 1}`}
                  fill
                  className="object-cover rounded-lg transition-transform duration-300 group-hover:scale-105 pointer-events-none"
                  sizes="(max-width: 768px) 256px, (max-width: 1024px) 320px, 384px"
                  draggable={false}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 rounded-lg"></div>
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-3">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                    </svg>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Gradient overlays */}
          <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-dark-bg to-transparent pointer-events-none z-10"></div>
          <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-dark-bg to-transparent pointer-events-none z-10"></div>

          {/* Pause indicator */}
          {isPaused && (
            <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm z-20">
              Paused
            </div>
          )}
        </div>
      </div>

      {/* Fullscreen Modal */}
      {selectedImage && (
        <div className="fixed inset-0 z-50 modal-overlay flex items-center justify-center p-4">
          <div className="relative max-w-7xl max-h-full">
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all duration-300"
            >
              <X size={24} />
            </button>
            
            <button
              onClick={prevImage}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all duration-300"
            >
              <ChevronLeft size={24} />
            </button>
            
            <button
              onClick={nextImage}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all duration-300"
            >
              <ChevronRight size={24} />
            </button>

            <Image
              src={selectedImage}
              alt="Portfolio fullscreen view"
              width={1200}
              height={800}
              className="max-w-full max-h-full object-contain"
              priority
            />
          </div>
        </div>
      )}
    </section>
  );
};

export default PortfolioSection;
