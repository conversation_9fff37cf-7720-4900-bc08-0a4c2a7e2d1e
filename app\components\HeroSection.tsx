'use client';

import { useEffect, useRef } from 'react';

const HeroSection = () => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch((error) => {
        console.log('Video autoplay failed:', error);
      });
    }
  }, []);

  return (
    <section id="home" className="relative h-screen w-full overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0 w-full h-full">
        <video
          ref={videoRef}
          className="w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
        >
          <source src="/video/video_slider.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
      </div>

      {/* Content overlay - minimal as requested */}
      <div className="relative z-10 h-full flex items-center justify-center">
        <div className="text-center px-4">
          {/* Optional: Add minimal branding or call-to-action here if needed */}
          <div className="animate-fade-in-up">
            <h1 className="font-syne text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight">
              36K AGENCY
            </h1>
            <p className="font-inter text-lg md:text-xl text-text-secondary max-w-2xl mx-auto mb-8">
              Agenzia creativa specializzata in social media management, content creation e strategie di marketing
            </p>
            <button
              onClick={() => {
                const element = document.querySelector('#about');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="gradient-button px-8 py-4 rounded-full font-inter font-semibold text-lg transition-all duration-300 hover:scale-105"
            >
              Scopri di più
            </button>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="animate-bounce">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
