'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';

const AboutSection = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // List of shooting images
  const shootingImages = [
    '/img/Foto/Shooting/output/IMG_3330.webp',
    '/img/Foto/Shooting/output/IMG_6493_TIF.webp',
    '/img/Foto/Shooting/output/IMG_6514_TIF.webp',
    '/img/Foto/Shooting/output/IMG_6532_TIF.webp',
    '/img/Foto/Shooting/output/_DSC0189.webp',
    '/img/Foto/Shooting/output/_DSC0405.webp',
    '/img/Foto/Shooting/output/_DSC1512.webp',
    '/img/Foto/Shooting/output/_DSC1601.webp',
    '/img/Foto/Shooting/output/_DSC1633.webp',
    '/img/Foto/Shooting/output/_DSC1674.webp',
    '/img/Foto/Shooting/output/_DSC1795.webp',
    '/img/Foto/Shooting/output/_DSC2040.webp',
    '/img/Foto/Shooting/output/_DSC2061.webp',
    '/img/Foto/Shooting/output/_DSC2182.webp',
    '/img/Foto/Shooting/output/_DSC3028.webp',
    '/img/Foto/Shooting/output/_DSC3363.webp',
    '/img/Foto/Shooting/output/_DSC5102.webp',
    '/img/Foto/Shooting/output/_DSC5976.webp',
    '/img/Foto/Shooting/output/_DSC8073.webp',
    '/img/Foto/Shooting/output/_DSC8101.webp',
    '/img/Foto/Shooting/output/_DSC9661.webp',
    '/img/Foto/Shooting/output/_DSC9770.webp',
    '/img/Foto/Shooting/output/_DSC9890.webp',
    '/img/Foto/Shooting/output/sara-04.webp',
    '/img/Foto/Shooting/output/sara-06.webp',
    '/img/Foto/Shooting/output/sara-26.webp',
    '/img/Foto/Shooting/output/sara-28.webp',
  ];

  const openModal = (imageSrc: string, index: number) => {
    setSelectedImage(imageSrc);
    setCurrentImageIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const nextImage = () => {
    const nextIndex = (currentImageIndex + 1) % shootingImages.length;
    setCurrentImageIndex(nextIndex);
    setSelectedImage(shootingImages[nextIndex]);
  };

  const prevImage = () => {
    const prevIndex = currentImageIndex === 0 ? shootingImages.length - 1 : currentImageIndex - 1;
    setCurrentImageIndex(prevIndex);
    setSelectedImage(shootingImages[prevIndex]);
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (selectedImage) {
        if (e.key === 'Escape') closeModal();
        if (e.key === 'ArrowRight') nextImage();
        if (e.key === 'ArrowLeft') prevImage();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedImage, currentImageIndex]);

  return (
    <section id="about" className="py-20 bg-soft-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-syne text-4xl md:text-5xl font-bold text-white mb-6">
            Chi Siamo
          </h2>
          <div className="max-w-4xl mx-auto">
            <p className="font-inter text-lg md:text-xl text-text-secondary leading-relaxed">
              36k Agency è un'agenzia creativa specializzata in social media Management, content creazione, 
              strategie di marketing e creazione di siti web e app. Ogni mese realizziamo centinaia di reel, 
              shooting fotografici, contenuti TikTok e campagne per brand, locali ed eventi in tutta Italia. 
              Lavoriamo con precisione, velocità e visione strategica, grazie a un team esperto. 
              Dall'idea iniziale alla pubblicazione, trasformiamo i contenuti in risultati.
            </p>
          </div>
        </div>

        {/* Photo Carousel */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {shootingImages.map((image, index) => (
            <div
              key={index}
              className="relative aspect-square cursor-pointer group overflow-hidden rounded-lg"
              onClick={() => openModal(image, index)}
            >
              <Image
                src={image}
                alt={`Shooting ${index + 1}`}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-110"
                sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Fullscreen Modal */}
      {selectedImage && (
        <div className="fixed inset-0 z-50 modal-overlay flex items-center justify-center p-4">
          <div className="relative max-w-7xl max-h-full">
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all duration-300"
            >
              <X size={24} />
            </button>
            
            <button
              onClick={prevImage}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all duration-300"
            >
              <ChevronLeft size={24} />
            </button>
            
            <button
              onClick={nextImage}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all duration-300"
            >
              <ChevronRight size={24} />
            </button>

            <Image
              src={selectedImage}
              alt="Fullscreen view"
              width={1200}
              height={800}
              className="max-w-full max-h-full object-contain"
              priority
            />
          </div>
        </div>
      )}
    </section>
  );
};

export default AboutSection;
