// Analytics utility functions for GA4 event tracking
// Respects user cookie consent preferences

interface EventParameters {
  category?: string;
  label?: string;
  value?: number;
  [key: string]: any;
}

// Check if analytics tracking is enabled based on user consent
export const isAnalyticsEnabled = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  try {
    const consent = localStorage.getItem('cookie-consent');
    if (!consent) return false;
    
    const parsedConsent = JSON.parse(consent);
    return parsedConsent.analytics === true;
  } catch {
    return false;
  }
};

// Generic event tracking function
export const trackEvent = (eventName: string, parameters: EventParameters = {}) => {
  if (!isAnalyticsEnabled() || typeof window === 'undefined' || !window.gtag) {
    console.log('Analytics tracking disabled or not available');
    return;
  }
  
  window.gtag('event', eventName, {
    event_category: parameters.category || 'engagement',
    event_label: parameters.label || '',
    value: parameters.value || undefined,
    ...parameters
  });
  
  console.log('GA4 Event tracked:', eventName, parameters);
};

// Specific tracking functions for common events

export const trackFormSubmission = (formName: string, success: boolean = true) => {
  trackEvent('form_submit', {
    category: 'form',
    label: formName,
    form_name: formName,
    success: success,
    value: success ? 1 : 0
  });
};

export const trackScrollDepth = (percentage: number) => {
  trackEvent('scroll_depth', {
    category: 'engagement',
    label: `${percentage}%`,
    value: percentage
  });
};

export const trackNavigationClick = (linkText: string, destination: string) => {
  trackEvent('navigation_click', {
    category: 'navigation',
    label: linkText,
    link_text: linkText,
    link_url: destination
  });
};

export const trackCTAClick = (buttonText: string, location: string) => {
  trackEvent('cta_click', {
    category: 'conversion',
    label: buttonText,
    button_text: buttonText,
    page_location: location
  });
};

export const trackImageClick = (imageAlt: string, imageSrc: string, category: string = 'image') => {
  trackEvent('image_click', {
    category: category,
    label: imageAlt,
    image_alt: imageAlt,
    image_src: imageSrc
  });
};

export const trackExternalLinkClick = (url: string, linkText: string = '') => {
  trackEvent('external_link_click', {
    category: 'outbound',
    label: url,
    link_text: linkText,
    external_url: url
  });
};

export const trackVideoPlay = (videoTitle: string, videoUrl: string) => {
  trackEvent('video_play', {
    category: 'media',
    label: videoTitle,
    video_title: videoTitle,
    video_url: videoUrl
  });
};

export const trackDownload = (fileName: string, fileType: string) => {
  trackEvent('file_download', {
    category: 'download',
    label: fileName,
    file_name: fileName,
    file_type: fileType
  });
};

export const trackSearch = (searchTerm: string, resultsCount: number = 0) => {
  trackEvent('search', {
    category: 'search',
    label: searchTerm,
    search_term: searchTerm,
    results_count: resultsCount
  });
};

export const trackSocialShare = (platform: string, url: string) => {
  trackEvent('social_share', {
    category: 'social',
    label: platform,
    social_platform: platform,
    shared_url: url
  });
};

// Page view tracking (for SPA navigation)
export const trackPageView = (pagePath: string, pageTitle: string) => {
  if (!isAnalyticsEnabled() || typeof window === 'undefined' || !window.gtag) {
    return;
  }
  
  window.gtag('config', 'G-5L8CYP0S8T', {
    page_path: pagePath,
    page_title: pageTitle,
  });
};

// Enhanced ecommerce tracking (for future use)
export const trackPurchase = (transactionId: string, value: number, currency: string = 'EUR', items: any[] = []) => {
  trackEvent('purchase', {
    category: 'ecommerce',
    transaction_id: transactionId,
    value: value,
    currency: currency,
    items: items
  });
};

// User engagement tracking
export const trackEngagement = (engagementType: string, duration?: number) => {
  trackEvent('user_engagement', {
    category: 'engagement',
    label: engagementType,
    engagement_type: engagementType,
    engagement_time_msec: duration
  });
};
