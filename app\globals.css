@import url('https://fonts.googleapis.com/css2?family=Syne:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: #0D0D0D;
  color: #FFFFFF;
  font-family: 'Inter', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1A1A1A;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #A020F0, #00E5FF);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #8A1AD1, #00C4D4);
}

/* Glass effect */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient button */
.gradient-button {
  background: linear-gradient(to right, #A020F0, #00E5FF);
  color: white;
  transition: all 0.3s ease;
}

.gradient-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(160, 32, 240, 0.3);
}



/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Portfolio carousel */
.portfolio-carousel {
  animation: scroll 40s linear infinite;
  width: calc(200% + 1rem);
}

.portfolio-carousel:hover {
  animation-play-state: paused;
}

/* Collaboration carousel */
.collab-carousel {
  animation: scroll 30s linear infinite;
  width: calc(200% + 2rem);
}

.collab-carousel:hover {
  animation-play-state: paused;
}

/* Modal styles */
.modal-overlay {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(5px);
}

/* Portfolio carousel */
.portfolio-carousel {
  animation: scroll 40s linear infinite;
  width: calc(200% + 1rem);
}

.portfolio-carousel:hover {
  animation-play-state: paused;
}

/* Collaboration carousel */
.collab-carousel {
  animation: scroll 30s linear infinite;
  width: calc(200% + 2rem);
}

.collab-carousel:hover {
  animation-play-state: paused;
}

/* Custom Tailwind Components */
@layer components {
  .gradient-button {
    @apply text-white transition-all duration-300;
    background: linear-gradient(-45deg, #A020F0, #00E5FF, #A020F0, #00E5FF);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
  }

  .gradient-button:hover {
    @apply transform scale-105 shadow-lg;
    box-shadow: 0 10px 25px rgba(160, 32, 240, 0.3);
    animation: gradientShift 1.5s ease infinite;
  }

  .gradient-border {
    @apply relative bg-dark-bg rounded-lg overflow-hidden;
    border: 2px solid transparent;
    background: linear-gradient(#1A1A1A, #1A1A1A) padding-box,
                linear-gradient(to right, #A020F0, #00E5FF) border-box;
  }

  .gradient-border:focus-within {
    background: linear-gradient(#1A1A1A, #1A1A1A) padding-box,
                linear-gradient(-45deg, #A020F0, #00E5FF, #A020F0, #00E5FF) border-box;
    background-size: 100% 100%, 400% 400%;
    animation: gradientBorderShift 2s ease infinite;
    box-shadow: 0 0 0 1px rgba(160, 32, 240, 0.3);
  }

  .gradient-border input,
  .gradient-border textarea {
    @apply relative z-10 bg-transparent;
  }

  .glass-effect {
    @apply backdrop-blur-lg bg-dark-bg/80 border border-white/10;
  }
}

/* Additional utility classes */
.placeholder-text-secondary::placeholder {
  color: #CCCCCC;
}

.placeholder-text-secondary::-webkit-input-placeholder {
  color: #CCCCCC;
}

.placeholder-text-secondary::-moz-placeholder {
  color: #CCCCCC;
}

.placeholder-text-secondary:-ms-input-placeholder {
  color: #CCCCCC;
}

/* Additional height classes */
.h-18 {
  height: 4.5rem;
}

/* Gradient border utility */
.border-gradient-primary {
  border-image: linear-gradient(to right, #A020F0, #00E5FF) 1;
}

/* Radial gradient utility */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* 3D perspective utilities */
.perspective-1000 {
  perspective: 1000px;
}

.transform-gpu {
  transform: translateZ(0);
}

/* Enhanced 3D tilt effects */
.hover\:rotate-2:hover {
  transform: perspective(1000px) rotateY(2deg) rotateX(-1deg) translateZ(10px);
}

.hover\:-rotate-2:hover {
  transform: perspective(1000px) rotateY(-2deg) rotateX(-1deg) translateZ(10px);
}

.hover\:rotate-1:hover {
  transform: perspective(1000px) rotateY(1deg) rotateX(-0.5deg) translateZ(8px);
}

.hover\:-rotate-1:hover {
  transform: perspective(1000px) rotateY(-1deg) rotateX(-0.5deg) translateZ(8px);
}

.hover\:rotate-3:hover {
  transform: perspective(1000px) rotateY(3deg) rotateX(-1.5deg) translateZ(12px);
}

.hover\:-rotate-3:hover {
  transform: perspective(1000px) rotateY(-3deg) rotateX(-1.5deg) translateZ(12px);
}

/* Animated gradient keyframes */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradientBorderShift {
  0% {
    background-position: 100% 100%, 0% 50%;
  }
  50% {
    background-position: 100% 100%, 100% 50%;
  }
  100% {
    background-position: 100% 100%, 0% 50%;
  }
}

/* Sidebar slide-in animation */
@keyframes slide-in {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in {
  animation: slide-in 0.4s ease-out forwards;
  opacity: 0;
}

/* Responsive sidebar adjustments */
@media (max-width: 480px) {
  .sidebar-mobile {
    width: 100vw;
  }
}

@media (max-width: 320px) {
  .sidebar-mobile {
    width: 100vw;
  }

  .sidebar-mobile .sidebar-content {
    padding: 1rem;
  }
}

/* Enhanced bg-gradient-primary with animation */
.bg-gradient-primary {
  background: linear-gradient(-45deg, #A020F0, #00E5FF, #A020F0, #00E5FF);
  background-size: 400% 400%;
  animation: gradientShift 3s ease infinite;
  transition: all 0.3s ease;
}

.bg-gradient-primary:hover {
  animation: gradientShift 1.5s ease infinite;
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(160, 32, 240, 0.3);
}


