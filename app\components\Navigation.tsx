'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { Menu, X, Home, Users, Handshake, Settings, Briefcase, Mail } from 'lucide-react';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent background scroll
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle click outside sidebar
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node) && isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const navItems = [
    { name: 'Home', href: '#home', icon: Home },
    { name: 'Chi Siamo', href: '#about', icon: Users },
    { name: 'Collaborazioni', href: '#collaborations', icon: Handshake },
    { name: 'Servizi', href: '#services', icon: Settings },
    { name: 'Portfolio', href: '#portfolio', icon: Briefcase },
    { name: 'Contatti', href: '#contact', icon: Mail },
  ];

  const sidebarRef = useRef<HTMLDivElement>(null);
  const touchStartX = useRef<number>(0);
  const touchCurrentX = useRef<number>(0);

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsOpen(false);
  };

  // Touch gesture handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchCurrentX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = () => {
    const deltaX = touchCurrentX.current - touchStartX.current;
    const threshold = 100; // Minimum swipe distance

    // Swipe right to close (positive deltaX)
    if (deltaX > threshold) {
      setIsOpen(false);
    }
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled ? 'glass-effect' : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-18 sm:h-20 lg:h-24">
          {/* Enhanced Logo */}
          <div className="flex-shrink-0">
            <Image
              src="/logo/logo.PNG"
              alt="36k Agency Logo"
              width={180}
              height={60}
              className="h-14 sm:h-16 md:h-20 lg:h-24 w-auto transition-all duration-300 hover:scale-105"
              priority
            />
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:block">
            <div className="ml-10 flex items-baseline space-x-8">
              {navItems.map((item) => (
                <button
                  key={item.name}
                  onClick={() => scrollToSection(item.href)}
                  className="font-inter text-white hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-r hover:from-purple-500 hover:to-cyan-400 px-3 py-2 text-sm font-medium transition-all duration-300"
                >
                  {item.name}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className={`relative inline-flex items-center justify-center p-2 rounded-md text-white hover:text-gray-300 hover:bg-gray-700/50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-purple-500 transition-all duration-300 ${
                isOpen ? 'bg-gray-700/50' : ''
              }`}
              aria-label="Toggle navigation menu"
            >
              <div className="relative w-6 h-6">
                <Menu
                  className={`absolute inset-0 w-6 h-6 transition-all duration-300 ${
                    isOpen ? 'opacity-0 rotate-180 scale-75' : 'opacity-100 rotate-0 scale-100'
                  }`}
                />
                <X
                  className={`absolute inset-0 w-6 h-6 transition-all duration-300 ${
                    isOpen ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 rotate-180 scale-75'
                  }`}
                />
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Backdrop Overlay */}
      <div
        className={`fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden transition-all duration-300 ${
          isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
        }`}
        onClick={() => setIsOpen(false)}
      />

      {/* Modern Sidebar Navigation */}
      <div
        ref={sidebarRef}
        className={`fixed top-0 right-0 h-full w-80 max-w-[100vw] sm:w-80 bg-dark-bg border-l border-border-gray z-50 lg:hidden transform transition-all duration-500 ease-out sidebar-mobile ${
          isOpen ? 'translate-x-0 shadow-2xl shadow-purple-500/20' : 'translate-x-full'
        }`}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-6 border-b border-border-gray bg-gradient-to-r from-purple-500/10 to-cyan-500/10">
          <div className="flex items-center space-x-3">
            <Image
              src="/logo/logo.PNG"
              alt="36k Agency Logo"
              width={120}
              height={40}
              className="h-10 w-auto"
            />
          </div>
          <button
            onClick={() => setIsOpen(false)}
            className="p-2 rounded-lg text-white hover:text-gray-300 hover:bg-gray-700/50 transition-all duration-300 hover:rotate-90"
            aria-label="Close navigation menu"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation Links */}
        <div className="flex flex-col py-6">
          {navItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <button
                key={item.name}
                onClick={() => scrollToSection(item.href)}
                className={`group flex items-center space-x-4 px-6 py-4 text-left transition-all duration-300 hover:bg-gradient-to-r hover:from-purple-500/20 hover:to-cyan-500/20 border-l-4 border-transparent hover:border-purple-500 ${
                  'animate-slide-in'
                }`}
                style={{
                  animationDelay: `${index * 100}ms`,
                  animationFillMode: 'both'
                }}
              >
                <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500/20 to-cyan-500/20 group-hover:from-purple-500/40 group-hover:to-cyan-500/40 transition-all duration-300">
                  <IconComponent className="w-5 h-5 text-white group-hover:text-purple-300 transition-colors duration-300" />
                </div>
                <span className="font-inter text-lg font-medium text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 transition-all duration-300">
                  {item.name}
                </span>
                <div className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full"></div>
                </div>
              </button>
            );
          })}
        </div>

        {/* Sidebar Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-border-gray bg-gradient-to-r from-purple-500/5 to-cyan-500/5">
          <div className="text-center">
            <p className="font-inter text-sm text-text-secondary">
              © 2024 36k Agency
            </p>
            <div className="mt-2 h-1 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full opacity-50"></div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
