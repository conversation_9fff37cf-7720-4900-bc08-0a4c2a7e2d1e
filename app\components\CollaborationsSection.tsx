'use client';

import Image from 'next/image';

const CollaborationsSection = () => {
  // List of collaboration logos
  const collaborationLogos = [
    '/img/loghi-collab/output/Ambra.webp',
    '/img/loghi-collab/output/Carmen.webp',
    '/img/loghi-collab/output/Luna.webp',
    '/img/loghi-collab/output/Piper club.webp',
    '/img/loghi-collab/output/STARPILATESPNG.webp',
    '/img/loghi-collab/output/afkstore.webp',
    '/img/loghi-collab/output/avoja.webp',
    '/img/loghi-collab/output/brondi.webp',
    '/img/loghi-collab/output/brunswick.webp',
    '/img/loghi-collab/output/caf-montesacro.webp',
    '/img/loghi-collab/output/cargo.webp',
    '/img/loghi-collab/output/cheers-events.webp',
    '/img/loghi-collab/output/chiusaporeaaa.webp',
    '/img/loghi-collab/output/cosimato fish lab.webp',
    '/img/loghi-collab/output/da-pina.webp',
    '/img/loghi-collab/output/di-ienno-immobiliare.webp',
    '/img/loghi-collab/output/eden-experience.webp',
    '/img/loghi-collab/output/excellence.webp',
    '/img/loghi-collab/output/foro italico 60.webp',
    '/img/loghi-collab/output/juicy-house.webp',
    '/img/loghi-collab/output/la-lanterna.webp',
    '/img/loghi-collab/output/ludovica-pagani.webp',
    '/img/loghi-collab/output/lumsa.webp',
    '/img/loghi-collab/output/mamma mia tour.webp',
    '/img/loghi-collab/output/nordhub.webp',
    '/img/loghi-collab/output/off-topic.webp',
    '/img/loghi-collab/output/officine-farneto.webp',
    '/img/loghi-collab/output/opus club.webp',
    '/img/loghi-collab/output/pablo-escobar-vodka.webp',
    '/img/loghi-collab/output/palazzo esposizione.webp',
    '/img/loghi-collab/output/palmatic.webp',
    '/img/loghi-collab/output/programmarti.webp',
    '/img/loghi-collab/output/sanremo giovani web.webp',
    '/img/loghi-collab/output/teatro centrale.webp',
    '/img/loghi-collab/output/tedx.webp',
    '/img/loghi-collab/output/tentami.webp',
    '/img/loghi-collab/output/toma-on-tour.webp',
    '/img/loghi-collab/output/triticum.webp',
    '/img/loghi-collab/output/vaticano.webp',
    '/img/loghi-collab/output/zephir.webp',
  ];

  // Duplicate the array for seamless infinite scroll
  const duplicatedLogos = [...collaborationLogos, ...collaborationLogos];

  return (
    <section id="collaborations" className="py-20 bg-dark-bg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-syne text-4xl md:text-5xl font-bold text-white mb-6">
            Le Nostre Collaborazioni
          </h2>
          <p className="font-inter text-lg md:text-xl text-text-secondary max-w-3xl mx-auto">
            Abbiamo avuto il privilegio di lavorare con brand, locali ed eventi di prestigio in tutta Italia
          </p>
        </div>

        {/* Auto-scrolling carousel */}
        <div className="relative overflow-hidden">
          <div className="flex space-x-8 collab-carousel">
            {duplicatedLogos.map((logo, index) => (
              <div
                key={index}
                className="flex-shrink-0 w-32 h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 relative group"
              >
                <div className="w-full h-full bg-white rounded-lg p-4 flex items-center justify-center transition-transform duration-300 group-hover:scale-105 group-hover:shadow-lg">
                  <Image
                    src={logo}
                    alt={`Collaboration ${index + 1}`}
                    fill
                    className="object-contain p-2"
                    sizes="(max-width: 768px) 128px, (max-width: 1024px) 160px, 192px"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Gradient overlays for smooth edges */}
          <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-dark-bg to-transparent pointer-events-none"></div>
          <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-dark-bg to-transparent pointer-events-none"></div>
        </div>
      </div>
    </section>
  );
};

export default CollaborationsSection;
