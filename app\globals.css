@import url('https://fonts.googleapis.com/css2?family=Syne:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: #0D0D0D;
  color: #FFFFFF;
  font-family: 'Inter', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1A1A1A;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #A020F0, #00E5FF);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #8A1AD1, #00C4D4);
}

/* Glass effect */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient button */
.gradient-button {
  background: linear-gradient(to right, #A020F0, #00E5FF);
  color: white;
  transition: all 0.3s ease;
}

.gradient-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(160, 32, 240, 0.3);
}

/* Gradient border */
.gradient-border {
  position: relative;
  background: #1A1A1A;
  border-radius: 8px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(to right, #A020F0, #00E5FF);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask-composite: xor;
}

.gradient-border:focus-within::before {
  background: linear-gradient(to right, #A020F0, #00E5FF);
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Portfolio carousel */
.portfolio-carousel {
  animation: scroll 40s linear infinite;
  width: calc(200% + 1rem);
}

.portfolio-carousel:hover {
  animation-play-state: paused;
}

/* Collaboration carousel */
.collab-carousel {
  animation: scroll 30s linear infinite;
  width: calc(200% + 2rem);
}

.collab-carousel:hover {
  animation-play-state: paused;
}

/* Modal styles */
.modal-overlay {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(5px);
}
