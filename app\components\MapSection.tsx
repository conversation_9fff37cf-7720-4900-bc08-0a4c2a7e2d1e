'use client';

import { useState } from 'react';
import { MapPin, ExternalLink } from 'lucide-react';

const MapSection = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  
  const address = "Via Val Maggia 28, 00141 Roma, Italy";
  const encodedAddress = encodeURIComponent(address);
  const googleMapsUrl = `https://www.google.com/maps/place/${encodedAddress}`;
  const embedUrl = `https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${encodedAddress}&zoom=15&maptype=roadmap`;
  
  // For demo purposes, using a generic embed URL without API key
  const demoEmbedUrl = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2969.123456789!2d12.5123456!3d41.9123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDHCsDU0JzQ0LjQiTiAxMsKwMzAnNDQuNCJF!5e0!3m2!1sen!2sit!4v1234567890123!5m2!1sen!2sit`;

  return (
    <section id="location" className="py-20 bg-dark-bg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-syne text-4xl md:text-5xl font-bold text-white mb-6">
            La Nostra Sede
          </h2>
          <p className="font-inter text-lg md:text-xl text-text-secondary max-w-3xl mx-auto">
            Vieni a trovarci nel nostro studio nel cuore di Roma
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Map Container */}
          <div className="relative">
            <div className="relative overflow-hidden rounded-xl border-2 border-border-gray hover:border-purple-500 transition-colors duration-300">
              {/* Map Iframe */}
              <div className="aspect-video bg-dark-bg">
                <iframe
                  src={demoEmbedUrl}
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="w-full h-full"
                  onLoad={() => setIsLoaded(true)}
                  title="36k Agency Location"
                />
                
                {/* Loading overlay */}
                {!isLoaded && (
                  <div className="absolute inset-0 bg-dark-bg flex items-center justify-center">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
                      <p className="text-text-secondary font-inter">Caricamento mappa...</p>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Dark overlay for better integration */}
              <div className="absolute inset-0 bg-black bg-opacity-20 pointer-events-none"></div>
            </div>
          </div>

          {/* Location Info */}
          <div className="space-y-8">
            <div className="bg-soft-black rounded-xl p-8 border border-border-gray">
              <div className="flex items-start space-x-4 mb-6">
                <div className="p-3 bg-gradient-primary rounded-lg flex-shrink-0">
                  <MapPin className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-syne text-xl font-semibold text-white mb-2">
                    Indirizzo
                  </h3>
                  <p className="font-inter text-text-secondary leading-relaxed">
                    {address}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-syne text-lg font-semibold text-white mb-2">
                    Come Raggiungerci
                  </h4>
                  <ul className="space-y-2 text-text-secondary font-inter">
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-gradient-primary rounded-full mr-3"></div>
                      Metro: Linea B - Fermata Jonio
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-gradient-primary rounded-full mr-3"></div>
                      Bus: Linee 38, 86, 340
                    </li>
                    <li className="flex items-center">
                      <div className="w-2 h-2 bg-gradient-primary rounded-full mr-3"></div>
                      Parcheggio disponibile nelle vicinanze
                    </li>
                  </ul>
                </div>

                <div className="pt-4 border-t border-border-gray">
                  <h4 className="font-syne text-lg font-semibold text-white mb-3">
                    Orari di Apertura
                  </h4>
                  <div className="space-y-2 text-text-secondary font-inter">
                    <div className="flex justify-between">
                      <span>Lunedì - Venerdì</span>
                      <span className="text-white">9:00 - 18:00</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Sabato</span>
                      <span className="text-white">9:00 - 13:00</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Domenica</span>
                      <span className="text-white">Chiuso</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-border-gray">
                <a
                  href={googleMapsUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-2 bg-gradient-primary text-white px-6 py-3 rounded-lg font-inter font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25"
                >
                  <ExternalLink className="w-5 h-5" />
                  <span>Apri in Google Maps</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MapSection;
