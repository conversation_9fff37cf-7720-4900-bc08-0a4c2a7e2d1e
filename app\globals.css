@import url('https://fonts.googleapis.com/css2?family=Syne:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: #0D0D0D;
  color: #FFFFFF;
  font-family: 'Inter', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1A1A1A;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #A020F0, #00E5FF);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #8A1AD1, #00C4D4);
}

/* Glass effect */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient button */
.gradient-button {
  background: linear-gradient(to right, #A020F0, #00E5FF);
  color: white;
  transition: all 0.3s ease;
}

.gradient-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(160, 32, 240, 0.3);
}

/* Gradient border */
.gradient-border {
  position: relative;
  background: #1A1A1A;
  border-radius: 8px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(to right, #A020F0, #00E5FF);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask-composite: xor;
}

.gradient-border:focus-within::before {
  background: linear-gradient(to right, #A020F0, #00E5FF);
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Portfolio carousel */
.portfolio-carousel {
  animation: scroll 40s linear infinite;
  width: calc(200% + 1rem);
}

.portfolio-carousel:hover {
  animation-play-state: paused;
}

/* Collaboration carousel */
.collab-carousel {
  animation: scroll 30s linear infinite;
  width: calc(200% + 2rem);
}

.collab-carousel:hover {
  animation-play-state: paused;
}

/* Modal styles */
.modal-overlay {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(5px);
}

/* Custom utility classes */
.bg-soft-black { background-color: #0D0D0D; }
.bg-dark-bg { background-color: #1A1A1A; }
.bg-gradient-primary { background: linear-gradient(to right, #A020F0, #00E5FF); }
.text-white { color: #FFFFFF; }
.text-text-secondary { color: #CCCCCC; }
.border-border-gray { border-color: #2E2E2E; }

.font-syne { font-family: 'Syne', sans-serif; }
.font-inter { font-family: 'Inter', sans-serif; }

.max-w-7xl { max-width: 80rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-md { max-width: 28rem; }

.mx-auto { margin-left: auto; margin-right: auto; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }

.text-center { text-align: center; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }
.text-6xl { font-size: 3.75rem; }
.text-7xl { font-size: 4.5rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }

.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }

.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.inline-flex { display: inline-flex; }

.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }

.w-full { width: 100%; }
.w-auto { width: auto; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-32 { width: 8rem; }

.h-auto { height: auto; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-32 { height: 8rem; }
.h-40 { height: 10rem; }
.h-48 { height: 12rem; }
.h-64 { height: 16rem; }
.h-80 { height: 20rem; }
.h-96 { height: 24rem; }
.h-screen { height: 100vh; }

.min-h-screen { min-height: 100vh; }

.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }

.top-0 { top: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.bottom-6 { bottom: 1.5rem; }
.right-6 { right: 1.5rem; }
.bottom-8 { bottom: 2rem; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.z-10 { z-index: 10; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

.overflow-hidden { overflow: hidden; }
.overflow-x-hidden { overflow-x: hidden; }

.border { border-width: 1px; }
.border-t { border-top-width: 1px; }

.cursor-pointer { cursor: pointer; }

.transition-all { transition: all; }
.duration-300 { transition-duration: 300ms; }

.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }

.transform { transform: translateZ(0); }
.-translate-x-1\/2 { transform: translateX(-50%); }
.-translate-y-1\/2 { transform: translateY(-50%); }

.leading-relaxed { line-height: 1.625; }

.aspect-square { aspect-ratio: 1 / 1; }

.object-cover { object-fit: cover; }
.object-contain { object-fit: contain; }

.fill { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }

.flex-shrink-0 { flex-shrink: 0; }
.flex-1 { flex: 1 1 0%; }

.group:hover .group-hover\:scale-105 { transform: scale(1.05); }
.group:hover .group-hover\:scale-110 { transform: scale(1.1); }
.group:hover .group-hover\:bg-opacity-30 { background-opacity: 0.3; }
.group:hover .group-hover\:opacity-100 { opacity: 1; }

.opacity-0 { opacity: 0; }
.opacity-75 { opacity: 0.75; }

.bg-opacity-0 { background-color: rgba(0, 0, 0, 0); }
.bg-opacity-30 { background-color: rgba(0, 0, 0, 0.3); }
.bg-opacity-40 { background-color: rgba(0, 0, 0, 0.4); }
.bg-opacity-50 { background-color: rgba(0, 0, 0, 0.5); }
.bg-opacity-70 { background-color: rgba(0, 0, 0, 0.7); }

.bg-black { background-color: #000000; }
.bg-white { background-color: #ffffff; }
.bg-green-500 { background-color: #10b981; }
.bg-green-600 { background-color: #059669; }
.bg-transparent { background-color: transparent; }

.text-green-400 { color: #34d399; }
.text-red-400 { color: #f87171; }
.text-purple-400 { color: #c084fc; }
.text-purple-500 { color: #a855f7; }
.text-cyan-400 { color: #22d3ee; }

.hover\:text-white:hover { color: #ffffff; }
.hover\:text-gray-300:hover { color: #d1d5db; }
.hover\:bg-green-600:hover { background-color: #059669; }
.hover\:bg-gray-700:hover { background-color: #374151; }
.hover\:bg-opacity-70:hover { background-color: rgba(0, 0, 0, 0.7); }

.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px; }
.focus\:ring-inset:focus { box-shadow: inset 0 0 0 2px; }
.focus\:ring-white:focus { box-shadow: 0 0 0 2px #ffffff; }
.focus\:ring-purple-500:focus { box-shadow: 0 0 0 2px #a855f7; }

.placeholder-text-secondary::placeholder { color: #CCCCCC; }

.animate-bounce { animation: bounce 1s infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-ping { animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; }
.animate-spin { animation: spin 1s linear infinite; }

@keyframes bounce {
  0%, 100% { transform: translateY(-25%); animation-timing-function: cubic-bezier(0.8,0,1,1); }
  50% { transform: none; animation-timing-function: cubic-bezier(0,0,0.2,1); }
}

@keyframes pulse {
  50% { opacity: .5; }
}

@keyframes ping {
  75%, 100% { transform: scale(2); opacity: 0; }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.disabled\:hover\:scale-100:disabled:hover { transform: scale(1); }

.whitespace-nowrap { white-space: nowrap; }
.resize-none { resize: none; }

/* Responsive classes */
@media (min-width: 640px) {
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:flex-row { flex-direction: row; }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:flex-row { flex-direction: row; }
  .md\:text-xl { font-size: 1.25rem; }
  .md\:text-5xl { font-size: 3rem; }
  .md\:text-6xl { font-size: 3.75rem; }
  .md\:w-40 { width: 10rem; }
  .md\:h-40 { height: 10rem; }
  .md\:w-80 { width: 20rem; }
  .md\:h-80 { height: 20rem; }
  .md\:mt-0 { margin-top: 0; }
}

@media (min-width: 1024px) {
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
  .lg\:h-10 { height: 2.5rem; }
  .lg\:h-20 { height: 5rem; }
  .lg\:text-7xl { font-size: 4.5rem; }
  .lg\:w-48 { width: 12rem; }
  .lg\:h-48 { height: 12rem; }
  .lg\:w-96 { width: 24rem; }
  .lg\:h-96 { height: 24rem; }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
}
