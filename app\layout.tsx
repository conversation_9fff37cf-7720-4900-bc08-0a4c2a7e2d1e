import type { Metada<PERSON> } from "next";
import { Syne, Inter } from "next/font/google";
import GoogleAnalytics from "./components/GoogleAnalytics";
import "./globals.css";

const syne = Syne({
  variable: "--font-syne",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export const metadata: Metadata = {
  title: "36k Agency - Agenzia Creativa Specializzata in Social Media Management",
  description: "36k Agency è un'agenzia creativa specializzata in social media management, content creation, strategie di marketing e creazione di siti web e app. Realizziamo centinaia di reel, shooting fotografici, contenuti TikTok e campagne per brand, locali ed eventi in tutta Italia.",
  keywords: "agenzia creativa, social media management, content creation, marketing, siti web, app, reel, shooting fotografici, TikTok, campagne, brand, eventi, Italia",
  authors: [{ name: "36k Agency" }],
  creator: "36k Agency",
  publisher: "36k Agency",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "it_IT",
    url: "https://36k.it",
    title: "36k Agency - Agenzia Creativa Specializzata in Social Media Management",
    description: "36k Agency è un'agenzia creativa specializzata in social media management, content creation, strategie di marketing e creazione di siti web e app.",
    siteName: "36k Agency",
  },
  twitter: {
    card: "summary_large_image",
    title: "36k Agency - Agenzia Creativa Specializzata in Social Media Management",
    description: "36k Agency è un'agenzia creativa specializzata in social media management, content creation, strategie di marketing e creazione di siti web e app.",
  },

};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="it" className={`${syne.variable} ${inter.variable}`}>
      <head>
        <link rel="icon" href="/favicon/favicon.ico" />
        <link rel="apple-touch-icon" href="/favicon/apple-icon.png" />
        <link rel="manifest" href="/favicon/manifest.json" />
      </head>
      <body className="font-inter bg-soft-black text-white">
        <GoogleAnalytics measurementId="G-5L8CYP0S8T" />
        {children}
      </body>
    </html>
  );
}
